package com.reagent.order.base.order.dto;


import com.ruijing.fundamental.api.annotation.ModelProperty;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * List类型extraValue响应DTO
 */
public class ListExtraValueResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ModelProperty("订单ID")
    private Integer orderId;

    @ModelProperty("extraKey对应的List数据，key为extraKey，value为对应的字符串列表")
    private Map<Integer, List<String>> extraValueMap;

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public Map<Integer, List<String>> getExtraValueMap() {
        return extraValueMap;
    }

    public void setExtraValueMap(Map<Integer, List<String>> extraValueMap) {
        this.extraValueMap = extraValueMap;
    }

    @Override
    public String toString() {
        return "ListExtraValueResponse{" +
                "orderId=" + orderId +
                ", extraValueMap=" + extraValueMap +
                '}';
    }
}
