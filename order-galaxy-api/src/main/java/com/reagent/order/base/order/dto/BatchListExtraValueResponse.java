package com.reagent.order.base.order.dto;

import com.ruijing.fundamental.api.annotation.ModelProperty;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 批量查询List类型extraValue响应DTO
 */
public class BatchListExtraValueResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ModelProperty("订单扩展值映射，第一层key为订单ID，第二层key为extraKey，value为对应的字符串列表")
    private Map<Integer, Map<Integer, List<String>>> orderExtraValueMap;

    public Map<Integer, Map<Integer, List<String>>> getOrderExtraValueMap() {
        return orderExtraValueMap;
    }

    public void setOrderExtraValueMap(Map<Integer, Map<Integer, List<String>>> orderExtraValueMap) {
        this.orderExtraValueMap = orderExtraValueMap;
    }

    @Override
    public String toString() {
        return "BatchListExtraValueResponse{" +
                "orderExtraValueMap=" + orderExtraValueMap +
                '}';
    }
}
